<template>
  <div class="sql-query-page">
    <!-- 顶部工具栏 -->
    <div class="query-toolbar">
      <div class="toolbar-left">
        <!-- 1. select实例/数据库框自动联动 -->
        <el-select v-model="selectedInstance" placeholder="选择数据库实例" style="width: 200px" @change="onInstanceChange"
          clearable filterable>
          <el-option v-for="instance in instances" :key="instance.id" :label="instance.alias || instance.instance_name"
            :value="instance.id" />
        </el-select>
        <el-select v-model="selectedDatabase" placeholder="选择数据库" style="width: 150px; margin-left: 10px"
          :disabled="!selectedInstance" @change="onDatabaseChange" clearable filterable>
          <el-option v-for="db in databases"
            :key="typeof db === 'string' ? db : (db.database_name || db.name || JSON.stringify(db))"
            :label="typeof db === 'string' ? db : (db.database_name || db.name || JSON.stringify(db))"
            :value="typeof db === 'string' ? db : (db.database_name || db.name || JSON.stringify(db))" />
        </el-select>
        <el-input-number v-model="limitNum" :min="1" :max="10000" :step="100" style="width: 160px; margin-left: 10px" />
        <el-button type="primary" :icon="VideoPlay" :loading="queryLoading"
          :disabled="!selectedInstance || !selectedDatabase || !currentTab?.sqlContent?.trim()" @click="executeQuery"
          style="margin-left: 10px">
          执行查询
        </el-button>
        <el-button :icon="Document"
          :disabled="!selectedInstance || !selectedDatabase || !currentTab?.sqlContent?.trim()" @click="explainQuery">
          执行计划
        </el-button>
        <el-button :icon="Refresh" @click="clearResult">清空结果</el-button>
        <el-button :icon="Download" :disabled="!resultTabs.length" @click="downloadResult">导出结果</el-button>
      </div>
      <div class="toolbar-right">
        <span class="status-info" v-if="resultTabs.length">共 {{ resultTabs.length }} 条SQL</span>
        <span class="connection-status" :class="{ connected: selectedInstance && selectedDatabase }">
          {{ selectedInstance && selectedDatabase ? '已连接' : '未连接' }}
        </span>
      </div>
    </div>
    <!-- 主要内容区域 -->
    <div class="query-content">
      <!-- 左侧数据库结构树 -->
      <div class="database-sidebar">
        <div class="sidebar-header">
          <span class="header-title">数据库实例</span>
          <el-button :icon="Refresh" size="small" text @click="refreshDatabaseTree" title="刷新" />
        </div>
        <!-- 新增搜索框 -->
        <div class="tree-search-bar">
          <el-input v-model="filterText" placeholder="搜索数据库、表" clearable size="small" />
        </div>
        <div class="tree-container">
          <el-empty v-if="!databaseTree.length" description="暂无数据库实例" :image-size="80">
            <template #description>
              <span>暂无数据库实例</span>
            </template>
            <el-button type="primary" size="small" @click="refreshDatabaseTree">刷新</el-button>
          </el-empty>
          <el-tree v-else :data="databaseTree" :props="treeProps" node-key="id" :expand-on-click-node="true"
            :load="loadTreeNode" lazy highlight-current @node-click="onTreeNodeClick"
            class="database-tree custom-db-tree" :filter-node-method="filterTreeNode" :default-expand-all="false"
            ref="dbTreeRef">
            <template #default="{ node, data }">
              <span class="tree-node custom-tree-node" :class="[data.type, { 'is-current': node.isCurrent }]">
                <el-icon class="node-icon">
                  <Connection v-if="data.type === 'instance'" />
                  <Folder v-else-if="data.type === 'database'" />
                  <Document v-else-if="data.type === 'table'" />
                  <View v-else-if="data.type === 'column'" />
                </el-icon>
                <span class="node-label">
                  <template v-if="data.type === 'instance'">
                    {{ data.label }}
                  </template>
                  <template v-else-if="data.type === 'database'">
                    <span class="db-label">{{ data.label }}</span>
                  </template>
                  <template v-else-if="data.type === 'table'">
                    <span class="table-label">{{ data.label }}</span>
                  </template>
                  <template v-else-if="data.type === 'column'">
                    <span class="column-label">{{ data.label }}</span>
                  </template>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <!-- 右侧查询区域 -->
      <div class="query-main">
        <!-- 多标签页SQL编辑器区域（保留原有） -->
        <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick" @tab-add="addTab" @tab-remove="removeTab"
          addable closable>
          <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name">
            <!-- SQL查询标签页 -->
            <div v-if="tab.type === 'query'" class="editor-section">
              <div class="section-header">
                <span class="section-title">SQL查询</span>
                <div class="header-actions">
                  <el-button size="small" text @click="formatSql">SQL美化</el-button>
                  <el-button size="small" text @click="tab.sqlContent = ''">清空</el-button>
                </div>
              </div>
              <div class="editor-wrapper">
                <!-- 添加加载状态 -->
                <div v-if="!editorReady" class="editor-loading">
                  <el-skeleton :rows="8" animated />
                  <div class="loading-text">正在加载SQL编辑器...</div>
                </div>
                <!-- Monaco编辑器 -->
                <MonacoEditor v-if="!editorFallback" ref="monacoEditorRef" v-model="tab.sqlContent"
                  :height="editorHeight" language="sql" theme="vs-dark" :options="editorOptions" @change="onSqlChange"
                  @ready="onEditorReady" />
                <!-- 备用文本区域 -->
                <div v-if="editorFallback" class="fallback-editor">
                  <el-input v-model="tab.sqlContent" type="textarea" :rows="12" placeholder="请输入SQL查询语句..."
                    @input="onSqlChange" class="sql-textarea" />
                  <div class="fallback-notice">
                    <el-alert title="编辑器加载失败，使用简化模式" type="warning" :closable="false" show-icon>
                      <template #default>
                        <div class="fallback-actions">
                          <el-button size="small" type="primary" @click="tryRecoverEditor">
                            尝试恢复编辑器
                          </el-button>
                        </div>
                      </template>
                    </el-alert>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表数据标签页 -->
            <div v-else-if="tab.type === 'table'" class="table-section">
              <div class="section-header">
                <span class="section-title">表: {{ tab.tableName }}</span>
                <div class="header-actions">
                  <el-button size="small" :icon="Refresh" @click="refreshTableData(tab)" :loading="tab.loading">
                    刷新数据
                  </el-button>
                  <!-- <el-button size="small" :icon="Document" @click="showTableInfo(tab)">
                    表结构
                  </el-button> -->
                </div>
              </div>

              <!-- 表数据内容 -->
              <div class="table-content">
                <!-- 加载状态 -->
                <div v-if="tab.loading" class="table-loading">
                  <el-skeleton :rows="8" animated />
                  <div class="loading-text">正在加载表数据...</div>
                </div>

                <!-- 表数据 -->
                <el-tabs v-else v-model="tab.activeTab" tab-position="left">
                  <el-tab-pane label="表数据[data]" name="data">                   
                    <el-alert v-if="tab.error" :title="tab.error" type="error" show-icon :closable="false" />
                    <div v-else>
                      <div class="table-stats" v-if="getRows(tab).length">
                        <span class="stat-item">共 {{ getRows(tab).length }} 条记录</span>
                        <span class="stat-item">显示前 {{ tab.limitNum || 100 }} 条</span>
                      </div>
                      <el-table
                        :data="getTableData(tab)"
                        border
                        stripe
                        height="100%"
                        style="width: 100%; min-width: 1200px;"
                        empty-text="表中暂无数据"
                        :scrollbar-always-on="true"
                      >
                        <el-table-column v-for="(column, index) in getColumns(tab)" :key="column + index"
                          :prop="`col_${index}`" :label="column" min-width="120" show-overflow-tooltip>
                          <template #default="scope">
                            <span v-if="scope.row[`col_${index}`] === null" class="null-value">NULL</span>
                            <span v-else-if="typeof scope.row[`col_${index}`] === 'object'" class="json-value">
                              {{ JSON.stringify(scope.row[`col_${index}`], null, 2) }}
                            </span>
                            <span v-else>{{ scope.row[`col_${index}`] }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="表结构[metadata]" name="table-info">
                    <el-descriptions :column="4" border>
                      <el-descriptions-item label="表名">{{ tab.tableInfo.meta_data.rows[0] }}</el-descriptions-item>
                      <el-descriptions-item label="表引擎">{{ tab.tableInfo.meta_data.rows[1] }}</el-descriptions-item>
                      <el-descriptions-item label="行格式">{{ tab.tableInfo.meta_data.rows[2] }}</el-descriptions-item>
                      <el-descriptions-item label="行数">{{ tab.tableInfo.meta_data.rows[3] }}</el-descriptions-item>
                      <el-descriptions-item label="平均行长度">{{ tab.tableInfo.meta_data.rows[4] }}</el-descriptions-item>
                      <el-descriptions-item label="数据长度">{{ tab.tableInfo.meta_data.rows[5] }}</el-descriptions-item>
                      <el-descriptions-item label="最大数据长度">{{ tab.tableInfo.meta_data.rows[6] }}</el-descriptions-item>
                      <el-descriptions-item label="索引长度">{{ tab.tableInfo.meta_data.rows[7] }}</el-descriptions-item>
                      <el-descriptions-item label="总大小">{{ tab.tableInfo.meta_data.rows[8] }}</el-descriptions-item>
                      <el-descriptions-item label="空闲大小">{{ tab.tableInfo.meta_data.rows[9] }}</el-descriptions-item>
                      <el-descriptions-item label="自增ID">{{ tab.tableInfo.meta_data.rows[10] }}</el-descriptions-item>
                      <el-descriptions-item label="字符集">{{ tab.tableInfo.meta_data.rows[11] }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ tab.tableInfo.meta_data.rows[12] }}</el-descriptions-item>
                      <el-descriptions-item label="检查时间">{{ tab.tableInfo.meta_data.rows[13] }}</el-descriptions-item>
                      <el-descriptions-item label="更新时间">{{ tab.tableInfo.meta_data.rows[14] }}</el-descriptions-item>
                      <el-descriptions-item label="注释">{{ tab.tableInfo.meta_data.rows[15] }}</el-descriptions-item>
                    </el-descriptions>
                  </el-tab-pane>
                  <el-tab-pane label="列信息[desc]" name="column-info">
                    <el-table :data="tab.tableInfo.desc.rows" border stripe height="100%" style="width: 100%" empty-text="表中暂无数据">
                      <el-table-column v-for="(column, index) in tab.tableInfo.desc.column_list" :key="column + index"
                        :prop="`column_name`" :label="column" min-width="120" show-overflow-tooltip>
                        <template #default="scope">
                          <span>{{ scope.row[index] }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                  <el-tab-pane label="索引信息[index]" name="index-info">
                    <el-table :data="tab.tableInfo.index.rows" border stripe height="100%" style="width: 100%" empty-text="表中暂无数据">
                      <el-table-column v-for="(column, index) in tab.tableInfo.index.column_list" :key="column + index"
                        :prop="`col_${index}`" :label="column" min-width="120" show-overflow-tooltip>
                        <template #default="scope">
                          <span>{{ scope.row[index] }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                  <el-tab-pane label="建表语句[ddl]" name="ddl-info">
                    <div v-if="tab.tableInfo && tab.tableInfo.create_sql && Array.isArray(tab.tableInfo.create_sql) && tab.tableInfo.create_sql.length && tab.tableInfo.create_sql[0].length > 1">
                      <MonacoEditor
                        v-model="tab.tableInfo.create_sql[0][1]"
                        height="600px"
                        language="sql"
                        theme="vs-light"
                        style="border-radius: 0px;"
                        :options="editorOptions"
                        @change="onSqlChange"
                        @ready="onEditorReady"
                      />
                    </div>
                    <div v-else-if="typeof tab.tableInfo.create_sql === 'string'">
                      <MonacoEditor
                        v-model="tab.tableInfo.create_sql"
                        height="600px"
                        language="sql"
                        theme="vs-light"
                        style="border-radius: 0px;"
                        :options="editorOptions"
                        @change="onSqlChange"
                        @ready="onEditorReady"
                      />
                    </div>
                    <el-empty v-else description="暂无建表语句" :image-size="80">
                      <template #description>
                        <span>暂无建表语句</span>
                      </template>
                    </el-empty>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 3. 查询结果区域：多SQL多tab展示 -->
        <el-tabs v-if="resultTabs.length" v-model="activeResultTab" type="card">
          <el-tab-pane v-for="(resTab, idx) in resultTabs" :key="'result-' + idx" :label="'结果' + (idx + 1)"
            :name="'result-' + idx">
            <div class="result-section">
              <div class="section-header">
                <span class="section-title">查询结果</span>
                <div class="result-stats" v-if="resTab.result && !resTab.error">
                  <span class="stat-item">
                    <el-icon>
                      <Timer />
                    </el-icon>
                    {{ resTab.queryTime }}ms
                  </span>
                  <span class="stat-item" v-if="resTab.affectedRows !== undefined">
                    <el-icon>
                      <Document />
                    </el-icon>
                    {{ resTab.affectedRows }} 行
                  </span>
                  <span class="stat-item" v-if="getRows(resTab).length">
                    <el-icon>
                      <View />
                    </el-icon>
                    {{ getRows(resTab).length }} 条记录
                  </span>
                </div>
              </div>
              <div class="result-wrapper">
                <el-alert v-if="resTab.error" :title="resTab.error" type="error" show-icon :closable="false"
                  class="error-alert" />
                <div v-if="getRows(resTab).length">
                  <div class="table-debug-info" v-if="false">
                    <pre>{{ JSON.stringify(getTableData(resTab), null, 2) }}</pre>
                  </div>
                  <el-table :data="getTableData(resTab)" border stripe :height="tableHeight" style="width: 100%"
                    class="result-table">
                    <el-table-column v-for="(column, index) in getColumns(resTab)" :key="column + index"
                      :prop="`col_${index}`" :label="column" min-width="120" show-overflow-tooltip>
                      <template #default="scope">
                        <span v-if="scope.row[`col_${index}`] === null">NULL</span>
                        <span v-else-if="typeof scope.row[`col_${index}`] === 'object'">
                          {{ JSON.stringify(scope.row[`col_${index}`]) }}
                        </span>
                        <span v-else>{{ scope.row[`col_${index}`] }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-empty v-if="!resTab.result || (getRows(resTab).length === 0 && !resTab.error)" description="暂无查询结果"
                  class="empty-result" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 表结构弹窗 -->
    <!-- <el-dialog v-model="showTableInfoDialog" :title="`表结构：${currentTableName}`" width="700px">
      <el-table :data="currentTableInfo" border stripe size="small">
        <el-table-column
          v-for="col in currentTableInfoColumns"
          :key="col"
          :prop="col"
          :label="col"
          min-width="80"
          show-overflow-tooltip
        />
      </el-table>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch, onErrorCaptured, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoPlay,
  Document,
  Refresh,
  Download,
  Folder,
  Connection,
  Timer,
  View
} from '@element-plus/icons-vue'
import { sqlApi, type DBInstance, type SqlQueryRequest } from '../../api/modules/sql'
import { handleAPIResponse } from '../../utils/response'
import MonacoEditor from '../../components/MonacoEditor.vue'
import { getMonaco, registerSqlCompletionProvider } from '../../utils/monaco-loader'

// SQL formatter with error handling
let sqlFormatter: any = null
try {
  sqlFormatter = require('sql-formatter').format
} catch (error) {
  console.warn('SQL formatter not available:', error)
}

// 1. select框联动
const selectedInstance = ref<number | null>(null)
const selectedDatabase = ref<string>('')
const currentTableNames = ref<string[]>([])
const limitNum = ref(100)

// 2. 监听数据库变化，自动获取表名并注册补全
watch(selectedDatabase, async (db) => {
  if (selectedInstance.value && db) {
    try {
      const res = await sqlApi.getTables(selectedInstance.value, { db_name: db })
      const result = handleAPIResponse(res) as any
      currentTableNames.value = result.items || result || []
      registerSqlCompletion() // 重新注册补全
    } catch (e) {
      currentTableNames.value = []
    }
  } else {
    currentTableNames.value = []
  }
})

// 3. 注册补全，合并表名
async function registerSqlCompletion() {
  try {
    // 获取Monaco实例
    const monaco = await getMonaco()

    // 合并所有关键字和表名
    const allKeywords = [
      ...sqlKeywords,
      ...currentTableNames.value,
      ...(currentTable.value ? [currentTable.value] : []),
      ...(currentColumns.value || [])
    ]

    // 使用monaco-loader中的函数注册SQL补全
    registerSqlCompletionProvider(allKeywords)

    console.log('SQL补全注册成功，关键字数量:', allKeywords.length)
  } catch (error) {
    console.error('注册SQL补全失败:', error)
  }
}

// 4. 树节点点击处理 - 修改为展开菜单和新开标签页
const onTreeNodeClick = async (data: any) => {
  if (data.type === 'instance') {
    // 点击实例时联动select框
    selectedInstance.value = data.instanceId
    selectedDatabase.value = ''
    await loadDatabases(data.instanceId)
  } else if (data.type === 'database') {
    // 点击数据库时联动select框
    selectedInstance.value = data.instanceId
    selectedDatabase.value = data.dbName
  } else if (data.type === 'table') {
    // 点击表名时新开标签页展示表数据和表信息
    await openTableTab(data)
  } else if (data.type === 'column') {
    // 点击列名时插入到编辑器
    insertToEditor(data.columnName)
  }
}

// 5. 执行查询支持多条SQL多tab结果
const resultTabs = ref<{ sql: string, result: any, error?: string, queryTime?: number, affectedRows?: number }[]>([])
const activeResultTab = ref('result-0')

/**
 * 执行SQL查询
 * 支持多条SQL语句，增强了结果处理和错误处理
 */
async function executeQuery() {
  if (!selectedInstance.value || !selectedDatabase.value || !currentTab.value?.sqlContent?.trim()) {
    ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
    return
  }

  queryLoading.value = true
  const sqls = currentTab.value.sqlContent.split(';').map(s => s.trim()).filter(Boolean)
  resultTabs.value = []

  try {
    for (let i = 0; i < sqls.length; i++) {
      const sql = sqls[i]
      const startTime = Date.now()

      try {
        const queryRequest: SqlQueryRequest = {
          sql_content: sql,
          db_name: selectedDatabase.value,
          limit_num: 1000
        }

        const response = await sqlApi.executeQuery(selectedInstance.value, queryRequest)
        console.log('SQL查询原始响应:', response)

        // 增强的结果处理，支持多种数据库类型的响应格式
        const processedResult = processQueryResult(response)
        console.log('处理后的查询结果:', processedResult)

        const queryTime = Date.now() - startTime

        resultTabs.value.push({
          sql,
          result: processedResult,
          queryTime,
          affectedRows: processedResult.affected_rows || 0
        })

        ElMessage.success(`SQL ${i + 1} 执行成功，耗时 ${queryTime}ms`)
      } catch (e: any) {
        const queryTime = Date.now() - startTime
        resultTabs.value.push({
          sql,
          result: null,
          error: e.message || '查询失败',
          queryTime
        })
        ElMessage.error(`SQL ${i + 1} 执行失败: ${e.message || '查询失败'}`)
      }
    }

    if (resultTabs.value.length > 0) {
      activeResultTab.value = 'result-0'
    }
  } finally {
    queryLoading.value = false
  }
}

/**
 * 处理查询结果，支持多种数据库类型的响应格式
 * @param response API响应
 * @returns 标准化的查询结果
 */
function processQueryResult(response: any) {
  try {
    let result = handleAPIResponse(response) as any

    // 如果处理失败，尝试直接使用响应数据
    if (!result) {
      result = response.data || response
    }

    // 标准化结果格式
    const standardResult: {
      rows: any[],
      column_list: string[],
      affected_rows: number,
      query_time: number,
      total_count: number
    } = {
      rows: [],
      column_list: [],
      affected_rows: 0,
      query_time: 0,
      total_count: 0
    }

    // 处理不同的响应格式
    if (result) {
      // MySQL/PostgreSQL 标准格式
      if (result.rows && result.column_list) {
        standardResult.rows = result.rows
        standardResult.column_list = result.column_list
      }
      // MongoDB 格式
      else if (result.documents) {
        standardResult.rows = result.documents
        if (result.documents.length > 0) {
          standardResult.column_list = Object.keys(result.documents[0])
        }
      }
      // Redis 格式
      else if (result.value !== undefined) {
        standardResult.rows = [[result.value]]
        standardResult.column_list = ['value']
      }
      // 通用数组格式
      else if (Array.isArray(result)) {
        standardResult.rows = result
        if (result.length > 0 && typeof result[0] === 'object') {
          standardResult.column_list = Object.keys(result[0])
        }
      }
      // 嵌套数据格式
      else if (result.data) {
        if (Array.isArray(result.data)) {
          standardResult.rows = result.data
          if (result.data.length > 0 && typeof result.data[0] === 'object') {
            standardResult.column_list = Object.keys(result.data[0])
          }
        } else if (result.data.rows) {
          standardResult.rows = result.data.rows
          standardResult.column_list = result.data.columns || result.data.column_list || []
        }
      }

      // 处理其他字段
      standardResult.affected_rows = result.affected_rows || result.rowsAffected || result.changes || 0
      standardResult.query_time = result.query_time || result.executionTime || 0
      standardResult.total_count = result.total_count || result.totalCount || standardResult.rows.length
    }

    console.log('标准化后的结果:', standardResult)
    return standardResult
  } catch (error) {
    console.error('处理查询结果失败:', error)
    return {
      rows: [],
      column_list: [],
      affected_rows: 0,
      query_time: 0,
      total_count: 0
    }
  }
}

// 方法
/**
 * 从实例数据更新数据库树
 */
const updateDatabaseTreeFromInstances = () => {
  if (instances.value.length > 0) {
    databaseTree.value = instances.value.map(instance => ({
      id: `instance_${instance.id}`,
      label: instance.alias || instance.instance_name || `实例 ${instance.id}`,
      type: 'instance',
      instanceId: instance.id,
      children: [],
      leaf: false
    }))
    console.log('更新后的树节点:', databaseTree.value)
  } else {
    databaseTree.value = []
  }
}

/**
 * 加载数据库实例列表
 * 支持多种API响应格式，增强了错误处理
 */
const loadInstances = async () => {
  try {
    // 静默加载，避免过多的消息提示
    const startTime = Date.now()

    // 发送API请求
    const response = await sqlApi.getDBInstances()
    console.log('原始API响应:', response)

    // 处理响应数据
    let result: any
    try {
      result = handleAPIResponse(response) as any
      console.log('处理后的实例数据:', result)
    } catch (parseError) {
      console.error('解析API响应失败:', parseError)
      // 尝试直接使用响应数据
      result = response.data || response
      console.log('使用原始响应数据:', result)
    }

    // 更新实例数据 - 支持多种数据格式
    if (result && result.items) {
      // 标准分页格式: { items: [...] }
      instances.value = result.items
      console.log('设置实例数据(items):', instances.value)
    } else if (result && result.data && Array.isArray(result.data.items)) {
      // 嵌套格式: { data: { items: [...] } }
      instances.value = result.data.items
      console.log('设置实例数据(data.items):', instances.value)
    } else if (result && result.data && Array.isArray(result.data)) {
      // 简单嵌套: { data: [...] }
      instances.value = result.data
      console.log('设置实例数据(data):', instances.value)
    } else if (Array.isArray(result)) {
      // 直接数组: [...]
      instances.value = result
      console.log('设置实例数据(array):', instances.value)
    } else if (typeof result === 'object' && result !== null) {
      // 尝试从对象中提取数组
      const possibleArrays = Object.values(result).filter(val => Array.isArray(val))
      if (possibleArrays.length > 0) {
        // 使用找到的第一个数组
        instances.value = possibleArrays[0] as any[]
        console.log('从对象中提取数组:', instances.value)
      } else {
        console.warn('未找到有效的实例数据数组')
        instances.value = []
      }
    } else {
      console.warn('未找到有效的实例数据')
      instances.value = []
    }

    // 更新树形菜单
    updateDatabaseTreeFromInstances()

    // 如果成功加载了实例，显示成功提示（仅在耗时超过500ms时显示）
    const loadTime = Date.now() - startTime
    if (instances.value.length > 0 && loadTime > 500) {
      ElMessage.success(`成功加载 ${instances.value.length} 个数据库实例`)
    } else if (instances.value.length === 0) {
      ElMessage.warning('未找到数据库实例')
    }
  } catch (error) {
    console.error('加载数据库实例失败:', error)
    ElMessage.error('加载数据库实例失败')
    instances.value = []
  }
}

const onInstanceChange = async (instanceId: number) => {
  selectedDatabase.value = ''
  databases.value = []
  // queryResult.value = null // 移除
  // queryError.value = '' // 移除

  // if (instanceId) {
  await loadDatabases(instanceId)
  await loadDatabaseTree(instanceId)
  // }
}

const loadDatabases = async (instanceId: number) => {
  try {
    const response = await sqlApi.getDatabases(instanceId)
    const result = handleAPIResponse(response) as any
    if (result && result.items) {
      databases.value = result.items || []
    }
  } catch (error) {
    console.error('加载数据库列表失败:', error)
    ElMessage.error('加载数据库列表失败')
  }
}

const onDatabaseChange = () => {
  // queryResult.value = null // 移除
  // queryError.value = '' // 移除
}

const explainQuery = async () => {
  if (!selectedInstance.value || !selectedDatabase.value || !currentTab.value.sqlContent.trim()) {
    ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
    return
  }

  try {
    const queryRequest: SqlQueryRequest = {
      sql_content: currentTab.value.sqlContent.trim(),
      db_name: selectedDatabase.value
    }

    const response = await sqlApi.explainQuery(selectedInstance.value, queryRequest)
    if (response.data && response.data.data) {
      // queryResult.value = response.data.data // 移除
      ElMessage.success('获取执行计划成功')
    }
  } catch (error: any) {
    console.error('获取执行计划失败:', error)
    // queryError.value = error.message || '获取执行计划失败' // 移除
    ElMessage.error('获取执行计划失败')
  }
}

const clearResult = () => {
  resultTabs.value = []
  activeResultTab.value = 'result-0'
  ElMessage.success('查询结果已清空')
}

// SQL编辑器变化事件
const onSqlChange = (value: string) => {
  if (currentTab.value) {
    currentTab.value.sqlContent = value
  }
}

const downloadResult = () => {
  if (!resultTabs.value.length) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  // 导出当前活跃的结果标签页
  const activeIndex = parseInt(activeResultTab.value.replace('result-', ''))
  const activeResult = resultTabs.value[activeIndex]

  if (!activeResult || !activeResult.result || !getRows(activeResult).length) {
    ElMessage.warning('当前结果页没有可导出的数据')
    return
  }

  exportResultToCsv(activeResult)
}

// 改进的CSV导出功能
const exportResultToCsv = (resultTab: any) => {
  const columns = getColumns(resultTab)
  const rows = getRows(resultTab)

  if (!columns.length || !rows.length) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 构建CSV内容
    const csvContent: string[] = []

    // 添加表头
    csvContent.push(columns.map((col: string) => `"${col}"`).join(','))

    // 添加数据行
    rows.forEach((row: any[]) => {
      const csvRow = row.map((cell: any) => {
        // 处理null、undefined和特殊字符
        if (cell === null || cell === undefined) {
          return '""'
        }
        // 转换为字符串并转义双引号
        const cellStr = String(cell).replace(/"/g, '""')
        return `"${cellStr}"`
      }).join(',')
      csvContent.push(csvRow)
    })

    // 创建并下载文件
    const csvString = csvContent.join('\n')
    const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8' }) // 添加BOM以支持中文
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `query_result_${new Date().getTime()}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 加载数据库树结构
const loadDatabaseTree = async (instanceId: number) => {
  try {
    // 查找实例
    const instance = instances.value.find(inst => inst.id === instanceId)
    console.log("加载数据库树 - 实例:", instance)

    if (!instance) {
      console.warn(`未找到ID为${instanceId}的实例`)
      return
    }

    // 加载数据库列表
    const response = await sqlApi.getDatabases(instanceId)
    console.log("数据库API响应:", response)

    const result = handleAPIResponse(response) as any
    console.log("处理后的数据库数据:", result)

    const databases = result.items || result || []
    console.log("数据库列表:", databases)

    if (databases.length) {
      // 创建树节点
      const treeData = [{
        id: `instance_${instanceId}`,
        label: instance.alias || instance.instance_name,
        type: 'instance',
        instanceId,
        children: databases.map((db: any) => {
          const dbName = typeof db === 'string' ? db : (db.database_name || db.name || db.toString())
          return {
            id: `db_${instanceId}_${dbName}`,
            label: dbName,
            type: 'database',
            instanceId,
            dbName,
            children: []
          }
        })
      }]

      console.log("创建的树数据:", treeData)
      databaseTree.value = treeData
    } else {
      console.warn(`实例${instanceId}没有数据库`)
      // 保持实例节点，但显示没有数据库
      const treeData = [{
        id: `instance_${instanceId}`,
        label: instance.alias || instance.instance_name,
        type: 'instance',
        instanceId,
        children: []
      }]
      databaseTree.value = treeData
    }
  } catch (error) {
    console.error('加载数据库树失败:', error)
    ElMessage.error('加载数据库列表失败')
  }
}

// 刷新数据库树
const refreshDatabaseTree = async () => {
  try {
    ElMessage.info('正在刷新数据库树...')
    await initDatabaseTree()
    ElMessage.success('数据库树刷新成功')
  } catch (error) {
    console.error('刷新数据库树失败:', error)
    ElMessage.error('刷新数据库树失败')
  }
}

// SQL关键字
const sqlKeywords = [
  'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'JOIN', 'LEFT', 'RIGHT', 'ON', 'GROUP BY', 'ORDER BY', 'LIMIT', 'AS', 'AND', 'OR', 'NOT', 'IN', 'IS', 'NULL', 'COUNT', 'SUM', 'AVG', 'MIN', 'MAX'
]
// 当前表/列元数据
const currentTable = ref<string>('')
const currentColumns = ref<string[]>([])
// 编辑器相关
const monacoEditorRef = ref<any>(null)
const editorReady = ref(false)
const editorInitialized = ref(false)
const editorFallback = ref(false) // 是否使用备用编辑器

// 编辑器就绪事件处理
const onEditorReady = () => {
  console.log('编辑器就绪')
  editorReady.value = true
  editorInitialized.value = true
}

// 编辑器错误事件处理
const onEditorError = (error: any) => {
  console.error('编辑器加载错误:', error)
  editorFallback.value = true
  editorReady.value = true
  ElMessage.error('编辑器加载失败: ' + (error.message || '未知错误'))
}

// 添加编辑器加载超时处理
const setupEditorTimeout = () => {
  // 如果8秒后编辑器仍未就绪，则显示备用文本区域
  setTimeout(() => {
    if (!editorReady.value) {
      console.warn('编辑器加载超时，启用备用编辑器')
      editorFallback.value = true // 启用备用编辑器
      editorReady.value = true // 强制显示编辑器区域
      editorInitialized.value = true // 标记为已初始化
      ElMessage.warning('SQL编辑器加载超时，已切换到简化模式')
    }
  }, 8000)
}

// 定义标签页类型
interface QueryTab {
  name: string
  label: string
  type: 'query'
  sqlContent: string
  result: any
  error: string
  queryLoading: boolean
  limitNum: number
}

interface TableTab {
  name: string
  label: string
  type: 'table'
  instanceId: string
  dbName: string
  tableName: string
  tableData: any[]
  tableInfo: any
  loading: boolean
  error: string
  sqlContent: string
  result: any
  queryLoading: boolean
  activeTab: string
  limitNum: number
}

type TabItem = QueryTab | TableTab

// 多标签页相关
const tabs = ref<TabItem[]>([
  {
    name: 'tab-1',
    label: 'SQL查询1',
    type: 'query',
    sqlContent: '',
    result: null,
    error: '',
    queryLoading: false,
    limitNum: limitNum.value
  }
])
const activeTab = ref('tab-1')
let tabIndex = 1
let tableTabIndex = 1
function addTab() {
  tabIndex++
  const name = `tab-${tabIndex}`
  tabs.value.push({
    name,
    label: `SQL查询${tabIndex}`,
    sqlContent: '',
    result: null,
    error: '',
    queryLoading: false,
    type: 'query',
    limitNum: limitNum.value
  })
  activeTab.value = name
}
function removeTab(targetName: string) {
  const idx = tabs.value.findIndex(tab => tab.name === targetName)
  if (idx !== -1) {
    tabs.value.splice(idx, 1)
    if (tabs.value.length) {
      activeTab.value = tabs.value[Math.max(0, idx - 1)].name
    }
  }
}
function handleTabClick(tab: any) {
  activeTab.value = tab.name
}
const currentTab = computed(() => tabs.value.find(tab => tab.name === activeTab.value) || tabs.value[0])

// 打开表数据标签页
async function openTableTab(data: any) {
  try {
    // 检查是否已经打开了该表的标签页
    const existingTabIndex = tabs.value.findIndex(tab =>
      tab.type === 'table' &&
      tab.instanceId === data.instanceId &&
      tab.dbName === data.dbName &&
      tab.tableName === data.tableName
    )

    if (existingTabIndex !== -1) {
      // 如果已经存在，切换到该标签页
      activeTab.value = tabs.value[existingTabIndex].name
      return
    }

    // 创建新的表标签页（用 reactive 保证响应式）
    tableTabIndex++
    const name = `table-${tableTabIndex}`
    const newTab = reactive({
      name,
      label: `${data.tableName}`,
      type: 'table' as const,
      instanceId: data.instanceId,
      dbName: data.dbName,
      tableName: data.tableName,
      tableData: [],
      tableInfo: null,
      loading: true,
      error: '',
      sqlContent: '',
      result: null,
      queryLoading: false,
      activeTab: 'data',
      limitNum: limitNum.value
    })

    // 添加到标签页列表
    tabs.value.push(newTab)
    activeTab.value = name

    // 加载表数据和表信息（串行，保证 loading 状态）
    try {
      await loadTableData(newTab)
      await loadTableInfo(newTab)
    } finally {
      newTab.loading = false
      await nextTick()
    }

    // 获取列名用于补全
    currentTable.value = data.tableName
    const tableTab = newTab as TableTab
    if (tableTab.tableInfo && (tableTab.tableInfo as any).desc?.rows) {
      currentColumns.value = (tableTab.tableInfo as any).desc.rows.map((col: any) =>
        col[0]
      )
    }
  } catch (error: any) {
    console.error('打开表标签页失败:', error)
    ElMessage.error(`打开表标签页失败: ${error.message || '未知错误'}`)
  }
}

// 加载表数据
async function loadTableData(tab: any) {
  try {
    tab.error = ''
    // 构建查询SQL
    const sql = `SELECT * FROM ${tab.tableName} LIMIT 100;`
    // 执行查询
    const queryRequest = {
      sql_content: sql,
      db_name: tab.dbName,
      limit_num: 100
    }
    tab.loading = true
    const response = await sqlApi.executeQuery(tab.instanceId, queryRequest)
    const result = handleAPIResponse(response) as any
    // 处理结果
    if (result && (result.rows || result.data)) {
      tab.tableData = result.rows || result.data || []
      tab.result = result
    } else {
      tab.tableData = []
      tab.result = result
      // tab.error = '无法获取表数据'
    }
  } catch (error: any) {
    console.error('加载表数据失败:', error)
    tab.error = `加载表数据失败: ${error.message || '未知错误'}`
  } finally {
    tab.loading = false
  }
}

// 加载表信息
async function loadTableInfo(tab: any) {
  try {
    // 获取表结构信息
    const response = await sqlApi.getTableInfo(
      tab.instanceId,
      { db_name: tab.dbName, table_name: tab.tableName }
    )
    const result = handleAPIResponse(response) as any
    tab.tableInfo = result.data || result
  } catch (error: any) {
    console.error('加载表信息失败:', error)
    // 不设置错误，因为表数据加载成功也可以显示
  }
}

// 刷新表数据
async function refreshTableData(tab: any) {
  await loadTableData(tab)
}

// 显示表结构信息
// function showTableInfo(tab: any) {
//   if (!tab.tableInfo || !tab.tableInfo.desc) {
//     ElMessage.warning('表结构信息未加载')
//     return
//   }
//   // 组装表结构数据
//   const columns = tab.tableInfo.desc.column_list
//   const rows = tab.tableInfo.desc.rows
//   currentTableInfo.value = rows.map((row: any[]) => {
//     const obj: any = {}
//     columns.forEach((col: string, idx: number) => {
//       obj[col] = row[idx]
//     })
//     return obj
//   })
//   currentTableName.value = tab.tableName
//   showTableInfoDialog.value = true
//   currentTableInfoColumns.value = columns
// }
// 插入内容到编辑器光标处
async function insertToEditor(text: string) {
  try {
    const editor = monacoEditorRef.value?.getEditorInstance?.()
    if (editor) {
      const position = editor.getPosition()
      const monaco = await getMonaco()

      editor.executeEdits('', [{
        range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
        text,
        forceMoveMarkers: true
      }])
      editor.focus()
    } else {
      // fallback
      if (currentTab.value) currentTab.value.sqlContent += text
    }
  } catch (error) {
    console.error('插入文本到编辑器失败:', error)
    // 确保即使失败也能插入文本
    if (currentTab.value) currentTab.value.sqlContent += text
  }
}

// 数据实例和数据库列表
const instances = ref<DBInstance[]>([])
const databases = ref<any[]>([])
// 数据库树相关
const databaseTree = ref<any[]>([])
const treeProps = { children: 'children', label: 'label', isLeaf: 'leaf' }
const dbTreeRef = ref()
const filterText = ref('')
// 查询loading
const queryLoading = ref(false)
// 编辑器高度/选项
const editorHeight = ref('300px')
// table高度
const tableHeight = ref(400)
// SQL美化
function formatSql() {
  if (currentTab.value.sqlContent.trim()) {
    currentTab.value.sqlContent = sqlFormatter(currentTab.value.sqlContent, { language: 'sql' })
  }
}
// 懒加载树节点
const loadTreeNode = async (node: any, resolve: any) => {
  console.log('懒加载节点:', node.data)
  try {
    if (node.level === 0) {
      // 根节点，直接返回实例列表
      console.log('加载根节点，返回实例列表:', instances.value)
      const instanceNodes = instances.value.map((instance: any) => ({
        id: `instance_${instance.id}`,
        label: instance.alias || instance.instance_name,
        type: 'instance',
        instanceId: instance.id,
        children: [],
        leaf: false
      }))
      resolve(instanceNodes)
    } else if (node.data.type === 'instance') {
      // 实例节点，加载数据库列表
      const response = await sqlApi.getDatabases(node.data.instanceId)
      const result = handleAPIResponse(response) as any
      const databases = result.items || result || []

      const dbNodes = databases.map((db: any) => ({
        id: `db_${node.data.instanceId}_${db.database_name || db}`,
        label: db.database_name || db,
        type: 'database',
        instanceId: node.data.instanceId,
        dbName: db.database_name || db,
        children: []
      }))
      resolve(dbNodes)
    } else if (node.data.type === 'database') {
      // 数据库节点，加载表列表
      const response = await sqlApi.getTables(node.data.instanceId, {
        db_name: node.data.dbName
      })
      const result = handleAPIResponse(response) as any
      const tables = result.items || result || []

      const tableNodes = tables.map((tableName: string) => ({
        id: `table_${node.data.instanceId}_${node.data.dbName}_${tableName}`,
        label: tableName,
        type: 'table',
        instanceId: node.data.instanceId,
        dbName: node.data.dbName,
        tableName,
        children: []
      }))
      resolve(tableNodes)
    } else if (node.data.type === 'table') {
      // 表节点，加载列信息
      const response = await sqlApi.getTableInfo(node.data.instanceId, {
        db_name: node.data.dbName,
        table_name: node.data.tableName
      })
      const result = handleAPIResponse(response) as any
      const columns = result.columns || []

      const columnNodes = columns.map((column: any) => ({
        id: `column_${node.data.instanceId}_${node.data.dbName}_${node.data.tableName}_${column.column_name}`,
        label: `${column.column_name} (${column.data_type})`,
        type: 'column',
        instanceId: node.data.instanceId,
        dbName: node.data.dbName,
        tableName: node.data.tableName,
        columnName: column.column_name,
        dataType: column.data_type,
        leaf: true
      }))
      resolve(columnNodes)
    } else {
      resolve([])
    }
  } catch (error) {
    console.error('加载树节点失败:', error)
    resolve([])
  }
}
// 编辑器选项 - 优化性能
const editorOptions = ref({
  fontSize: 14,
  wordWrap: 'on' as const,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  automaticLayout: true,
  theme: 'vs-dark',
  // 性能优化选项
  renderWhitespace: 'none' as const,
  renderControlCharacters: false,
  renderIndentGuides: false,
  renderValidationDecorations: 'editable' as const,
  // 减少初始化时的计算量
  folding: false,
  glyphMargin: false,
  // 延迟加载
  quickSuggestionsDelay: 200,
  // 减少不必要的渲染
  occurrencesHighlight: 'off' as const,
  // 禁用不需要的功能
  links: false,
  contextmenu: false
})
/**
 * 获取查询结果的行数据
 * 增强版：支持多种数据格式，提供更好的错误处理
 * @param tab 结果标签页
 * @returns 行数据数组
 */
function getRows(tab: any): any[] {
  // 检查是否有结果
  if (!tab || !tab.result) {
    return []
  }

  try {
    // 尝试获取行数据
    const rows = tab.result.rows

    // 检查行数据是否为数组
    if (Array.isArray(rows)) {
      return rows
    }

    // 尝试其他可能的属性名
    const possibleRowProps = ['data', 'records', 'list', 'items', 'documents', 'results']
    for (const prop of possibleRowProps) {
      if (Array.isArray(tab.result[prop])) {
        return tab.result[prop]
      }
    }

    // 如果结果本身是数组，则直接返回
    if (Array.isArray(tab.result)) {
      return tab.result
    }

    // 特殊情况：单个值结果（如Redis GET命令）
    if (tab.result.value !== undefined) {
      return [[tab.result.value]]
    }

    // 特殊情况：对象结果（如MongoDB查询单个文档）
    if (typeof tab.result === 'object' && !Array.isArray(tab.result) && tab.result !== null) {
      // 排除一些不应该被视为数据的属性
      const excludedProps = ['column_list', 'columns', 'fields', 'affected_rows', 'query_time', 'total_count']
      const dataProps = Object.keys(tab.result).filter(key => !excludedProps.includes(key))

      if (dataProps.length > 0) {
        // 尝试找到最可能是数据的属性
        for (const prop of dataProps) {
          const value = tab.result[prop]
          if (Array.isArray(value)) {
            return value
          }
        }

        // 如果没有找到数组属性，将整个对象作为单行返回
        return [tab.result]
      }
    }
  } catch (error) {
    console.error('获取行数据失败:', error)
  }

  // 默认返回空数组
  return []
}

/**
 * 获取查询结果的列名
 * 增强版：支持多种数据格式，提供更好的错误处理
 * @param tab 结果标签页
 * @returns 列名数组
 */
function getColumns(tab: any): string[] {
  // 检查是否有结果
  if (!tab || !tab.result) {
    return []
  }

  try {
    // 尝试获取列数据
    const columns = tab.result.column_list || tab.result.columns || tab.result.fields

    // 检查列数据是否为数组
    if (Array.isArray(columns)) {
      // 处理列对象数组 (如 [{name: 'id'}, {name: 'name'}])
      if (columns.length > 0 && typeof columns[0] === 'object' && columns[0].name) {
        return columns.map(col => col.name)
      }
      return columns
    }

    // 如果有行数据，但没有列数据，尝试从第一行推断列
    const rows = getRows(tab)
    if (rows.length > 0) {
      if (Array.isArray(rows[0])) {
        // 如果行是数组，则生成列名
        return Array.from({ length: rows[0].length }, (_, i) => `列 ${i + 1}`)
      } else if (typeof rows[0] === 'object' && rows[0] !== null) {
        // 如果行是对象，则使用对象的键作为列名
        return Object.keys(rows[0])
      }
    }

    // 特殊情况：单个值结果
    if (tab.result.value !== undefined) {
      return ['值']
    }
  } catch (error) {
    console.error('获取列数据失败:', error)
  }

  // 默认返回空数组
  return []
}

/**
 * 格式化表格数据，将行数组转换为对象数组
 * 增强版：支持多种数据格式，提供更好的错误处理
 * @param tab 结果标签页
 * @returns 格式化后的表格数据
 */
function getTableData(tab: any): any[] {
  const rows = getRows(tab)
  const columns = getColumns(tab)

  if (!rows.length || !columns.length) {
    return []
  }

  try {
    // 检查行数据类型
    if (rows.length > 0) {
      // 如果行已经是对象数组，检查是否需要转换
      if (typeof rows[0] === 'object' && !Array.isArray(rows[0]) && rows[0] !== null) {
        // 检查对象的键是否与列名匹配
        const firstRowKeys = Object.keys(rows[0])
        const allKeysMatch = columns.every(col => firstRowKeys.includes(col))

        if (allKeysMatch) {
          // 如果键已经匹配列名，则直接使用对象数组
          return rows.map(row => {
            // 确保所有列都有值
            const formattedRow: any = {}
            columns.forEach((col, index) => {
              formattedRow[`col_${index}`] = row[col]
            })
            return formattedRow
          })
        }
      }

      // 处理数组行
      if (Array.isArray(rows[0])) {
        return rows.map((row: any[]) => {
          const rowObj: any = {}
          row.forEach((cell, index) => {
            rowObj[`col_${index}`] = cell
          })
          return rowObj
        })
      }
    }

    // 处理其他情况
    return rows.map((row: any) => {
      const rowObj: any = {}

      // 如果是对象，尝试使用列名作为键
      if (typeof row === 'object' && row !== null) {
        columns.forEach((col, index) => {
          rowObj[`col_${index}`] = row[col] !== undefined ? row[col] : null
        })
      }
      // 如果是基本类型，将其作为单个值
      else {
        rowObj.col_0 = row
      }

      return rowObj
    })
  } catch (error) {
    console.error('格式化表格数据失败:', error)
    return []
  }
}

// 搜索过滤方法
function filterTreeNode(value: string, data: any) {
  if (!value) return true
  return (data.label && data.label.toLowerCase().includes(value.toLowerCase()))
}
// 监听搜索框变化，实时过滤
watch(filterText, val => {
  dbTreeRef.value && dbTreeRef.value.filter(val)
})

// 监听实例数据变化，自动更新树形菜单
watch(instances, (newInstances) => {
  console.log('实例数据发生变化:', newInstances)
  if (newInstances && newInstances.length > 0) {
    databaseTree.value = newInstances.map(instance => ({
      id: `instance_${instance.id}`,
      label: instance.alias || instance.instance_name,
      type: 'instance',
      instanceId: instance.id,
      children: [],
      leaf: false
    }))
    console.log('更新后的树节点:', databaseTree.value)
  } else {
    databaseTree.value = []
  }
}, { immediate: true, deep: true })

// 初始化数据库树
const initDatabaseTree = async () => {
  try {
    console.log('开始初始化数据库树...')

    // 先设置一个加载中的状态，提供更好的用户体验
    databaseTree.value = [{
      id: 'loading',
      label: '正在加载数据库实例...',
      type: 'loading',
      disabled: true,
      leaf: true
    }]

    // 异步加载实例数据
    await loadInstances()
    console.log("初始化完成，实例数据:", instances.value)

    // 如果没有实例数据，显示空状态
    if (!instances.value.length) {
      databaseTree.value = [{
        id: 'empty',
        label: '未找到数据库实例',
        type: 'empty',
        disabled: true,
        leaf: true
      }]
    }
  } catch (error) {
    console.error('初始化数据库树失败:', error)
    ElMessage.error('加载数据库实例失败')

    // 显示错误状态
    databaseTree.value = [{
      id: 'error',
      label: '加载失败，点击刷新按钮重试',
      type: 'error',
      disabled: true,
      leaf: true
    }]
  }
}



/**
 * 延迟初始化编辑器
 * 使用优化的加载策略，提高性能
 */
const initializeEditor = () => {
  if (!editorInitialized.value) {
    console.log('初始化SQL补全')

    // 使用微任务队列，避免阻塞UI
    Promise.resolve().then(() => {
      try {
        registerSqlCompletion()
        console.log('SQL补全注册成功')
      } catch (error) {
        console.error('注册SQL补全失败:', error)
      }
    })
  }
}

/**
 * 尝试恢复编辑器
 * 当编辑器加载失败时，尝试重新加载
 */
const tryRecoverEditor = () => {
  if (editorFallback.value) {
    editorFallback.value = false
    editorReady.value = false

    // 显示正在恢复的消息
    ElMessage.info('正在尝试恢复编辑器...')

    // 设置新的超时处理
    setupEditorTimeout()

    // 延迟一点时间再尝试初始化
    setTimeout(() => {
      initializeEditor()
    }, 100)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 并行加载数据库树和编辑器，提高加载速度
  Promise.all([
    // 加载数据库树
    initDatabaseTree().catch(error => {
      console.error('初始化数据库树失败:', error)
    }),

    // 设置编辑器加载超时处理
    Promise.resolve().then(() => {
      setupEditorTimeout()
    })
  ])

  // 使用更高效的加载策略
  if ('requestIdleCallback' in window) {
    // 使用空闲时间初始化编辑器
    (window as any).requestIdleCallback(() => {
      initializeEditor()
    }, { timeout: 2000 })
  } else {
    // 降级处理：使用微任务队列
    queueMicrotask(() => {
      setTimeout(() => {
        initializeEditor()
      }, 100)
    })
  }
})

// 错误捕获处理
onErrorCaptured((err, _instance, info) => {
  console.error('组件错误:', err, info)

  // 如果是编辑器相关错误，启用备用编辑器
  if (info.includes('editor') || info.includes('monaco')) {
    editorFallback.value = true
    editorReady.value = true
    ElMessage.error('编辑器加载失败，已切换到简化模式')
  }

  // 不阻止错误继续传播
  return false
})

// 窗口大小变化时重新计算编辑器和表格高度
const updateSizes = () => {
  const windowHeight = window.innerHeight
  const toolbarHeight = 60
  const tabsHeight = 40
  const headerHeight = 40
  const availableHeight = windowHeight - toolbarHeight - tabsHeight - headerHeight - 100

  editorHeight.value = Math.max(200, Math.floor(availableHeight * 0.4)) + 'px'
  tableHeight.value = Math.max(200, Math.floor(availableHeight * 0.6))
}

onMounted(() => {
  updateSizes()
  window.addEventListener('resize', updateSizes)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateSizes)
})

// ... existing code ...
// 表结构弹窗相关
const showTableInfoDialog = ref(false)
const currentTableInfo = ref([])
const currentTableName = ref('')
const currentTableInfoColumns = ref<string[]>([])
// ... existing code ...

// 新增：获取tab下各section数据
function getTabSection(tab, type) {
  console.log('getTabSection', tab, type)
  if (!tab.result) return { columns: [], rows: [] }
  const section = tab.result[type]
  if (!section) return { columns: [], rows: [] }
  const columns = section.column_list || []
  // rows: 二维数组转对象数组
  const rows = (section.rows || []).map(row =>
    Object.fromEntries(row.map((val, idx) => [`col_${idx}`, val]))
  )
  return { columns, rows }
}
</script>

<style scoped>
.sql-query-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.query-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-info {
  font-size: 12px;
  color: #909399;
  padding: 4px 8px;
  background: #f4f4f5;
  border-radius: 4px;
}

.connection-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.connection-status.connected {
  color: #67c23a;
  background: #f0f9ff;
  border-color: #b3d8ff;
}

.query-content {
  flex: 1;
  display: flex;
  /* gap: 12px; */
  /* padding: 12px; */
  overflow: hidden;
}

.database-sidebar {
  width: 280px;
  background: white;
  /* border-radius: 8px; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-title {
  font-weight: 500;
  color: #303133;
}

.tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.tree-search-bar {
  padding: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.database-tree {
  background: transparent;
}

.custom-db-tree {
  font-size: 13px;
}

.custom-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.custom-tree-node.instance {
  font-weight: 500;
  color: #409eff;
}

.custom-tree-node.database {
  color: #67c23a;
}

.custom-tree-node.table {
  color: #606266;
}

.custom-tree-node.column {
  color: #909399;
  font-style: italic;
}

.custom-tree-node.is-current {
  background-color: #ecf5ff;
  border-radius: 4px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
}

.node-icon {
  font-size: 14px;
}

.node-label {
  font-size: 13px;
  color: #606266;
}

.query-main {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-section {
  border-bottom: 1px solid #e4e7ed;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.section-title {
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.result-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
  padding: 2px 6px;
  background: #f4f4f5;
  border-radius: 4px;
}

.stat-item .el-icon {
  font-size: 14px;
}

.editor-wrapper {
  padding: 12px;
  position: relative;
}

.editor-loading {
  padding: 20px;
  background-color: #1e1e1e;
  border-radius: 4px;
  min-height: 200px;
}

.loading-text {
  text-align: center;
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.fallback-editor {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 8px;
}

.sql-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.sql-textarea :deep(.el-textarea__inner) {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border: 1px solid #3c3c3c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.sql-textarea :deep(.el-textarea__inner):focus {
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.fallback-notice {
  margin-top: 8px;
}

.fallback-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

/* 表数据标签页样式 */
.table-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.el-table__body-wrapper {
  overflow: auto !important;
}

.table-loading {
  padding: 20px;
}

.table-stats {
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.table-stats .stat-item {
  margin-right: 16px;
  font-size: 13px;
  color: #606266;
}

.null-value {
  color: #909399;
  font-style: italic;
}

.json-value {
  color: #409eff;
  font-family: monospace;
}

.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.result-wrapper {
  flex: 1;
  overflow: hidden;
  padding: 12px;
}

.error-alert {
  margin-bottom: 12px;
}

.result-table {
  border-radius: 6px;
  overflow: hidden;
}

.result-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}

.result-table :deep(.el-table__header-row th) {
  background-color: #f0f2f5;
  color: #606266;
  font-weight: bold;
  padding: 8px 0;
}

.result-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.result-table :deep(.el-table__cell) {
  padding: 6px 8px;
}

.result-table :deep(.cell) {
  line-height: 1.5;
  word-break: break-all;
}

.table-debug-info {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow: auto;
}

.empty-result {
  margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .database-sidebar {
    width: 240px;
  }

  .toolbar-left {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .query-content {
    flex-direction: column;
    gap: 8px;
  }

  .database-sidebar {
    width: 100%;
    height: 200px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .toolbar-right {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .sql-query-page {
    background-color: #1a1a1a;
  }

  .query-toolbar,
  .database-sidebar,
  .query-main {
    background: #2d2d2d;
    border-color: #404040;
  }

  .sidebar-header,
  .section-header {
    background: #363636;
    border-color: #404040;
  }

  .header-title,
  .section-title {
    color: #e4e7ed;
  }

  .node-label {
    color: #c0c4cc;
  }

  .status-info {
    background: #404040;
    color: #c0c4cc;
  }
}

/* 滚动条样式 */
.tree-container::-webkit-scrollbar,
.result-wrapper::-webkit-scrollbar {
  width: 6px;
}

.tree-container::-webkit-scrollbar-track,
.result-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb,
.result-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb:hover,
.result-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.query-toolbar,
.database-sidebar,
.query-main {
  transition: all 0.3s ease;
}

.tree-node:hover .node-label {
  color: #409eff;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 表格优化 */
.result-table :deep(.el-table__cell) {
  padding: 8px 12px;
}

.result-table :deep(.el-table__header-wrapper) {
  background: #f8f9fa;
}

/* 标签页样式优化 */
:deep(.el-tabs__header) {
  margin: 0;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 16px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sql-query-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* 顶部工具栏 */
.query-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.status-info {
  font-size: 12px;
  color: #909399;
  margin-left: 16px;
}

.status-info {
  font-size: 12px;
  color: #909399;
  margin-left: 16px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafbfc;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.tree-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.database-tree {
  height: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
}

.node-icon {
  font-size: 16px;
  color: #606266;
}

.node-label {
  font-size: 13px;
  color: #606266;
}

/* 右侧查询区域 */
.query-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
}

.node-icon {
  font-size: 16px;
  color: #606266;
}

.node-label {
  font-size: 13px;
  color: #606266;
}

/* 右侧查询区域 */
.query-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.custom-db-tree {
  background: transparent;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 4px 0 4px 0;
  min-height: 400px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  padding: 4px 0 4px 4px;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
}

.custom-tree-node:hover {
  background: #f0f7ff;
  color: #409eff;
}

.custom-tree-node.is-current {
  background: #e6f7ff;
  color: #409eff;
  font-weight: 600;
}

.custom-tree-node .node-icon {
  font-size: 16px;
  color: #b3b3b3;
  transition: color 0.2s, transform 0.2s;
}

.custom-tree-node:hover .node-icon,
.custom-tree-node.is-current .node-icon {
  color: #409eff;
  transform: scale(1.1);
}

.custom-tree-node .db-label {
  color: #67c23a;
  font-weight: 500;
}

.custom-tree-node .table-label {
  color: #409eff;
}

.custom-tree-node .column-label {
  color: #606266;
  font-size: 13px;
}

.tree-search-bar {
  padding: 8px 12px 0 12px;
  background: #fafbfc;
}
</style>