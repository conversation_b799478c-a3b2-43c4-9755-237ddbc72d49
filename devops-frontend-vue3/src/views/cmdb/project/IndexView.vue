<template>
  <div class="project-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h3>项目管理</h3>
        <p class="page-description">管理DevOps平台中的项目配置和设置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAdd" :icon="Plus">
          新建项目
        </el-button>
      </div>
    </div>
    

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-container">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="6" :lg="5">
            <el-input 
              v-model="searchForm.name"
              placeholder="搜索项目名称"
              clearable
              prefix-icon="Search"
              @input="handleSearch"
              class="search-input"
            />
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="5">
            <el-select 
              v-model="searchForm.productId"
              placeholder="选择产品"
              clearable
              @change="handleSearch"
              class="search-select"
            >
              <el-option
                v-for="product in products"
                :key="product.id"
                :label="product.name"
                :value="product.id"
              />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="5">
            <div class="action-buttons">
              <el-button @click="handleReset" class="refresh-btn">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 项目列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <el-icon><List /></el-icon>
            项目列表
          </div>
          <div class="table-actions">
            <el-tooltip content="刷新数据" placement="top">
              <el-button
                type="text"
                @click="fetchProjects"
                :loading="loading"
                class="icon-btn"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <!-- PC端表格头部 -->
      <!-- <div class="table-header-row desktop-only">
        <div class="header-cell code-col">项目代码</div>
        <div class="header-cell name-col">项目名称</div>
        <div class="header-cell product-col">所属产品</div>
        <div class="header-cell personnel-col">人员配置</div>
        <div class="header-cell time-col">创建时间</div>
        <div class="header-cell action-col">操作</div>
      </div> -->

      <!-- 数据列表 -->
      <div v-loading="loading" class="data-container">
          <!-- PC端表格 -->
      <div class="desktop-table desktop-only">
        <el-table
          :data="projects"
          v-loading="loading"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="project_code" label="项目代码" width="140">
            <template #default="{ row }">
              <el-tag type="primary" size="small" class="code-tag">
                {{ row.project_code }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="项目名称" min-width="180" />
          <el-table-column prop="product" label="所属产品" width="150">
            <template #default="{ row }">
              <div v-if="row.product_id" class="product-info">
                <el-tag type="info" size="small">{{ getProductName(row.product_id) }}</el-tag>
              </div>
              <span v-else class="empty-text">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="managers" label="人员配置" width="220">
            <template #default="{ row }">
              <div class="personnel-config">
                <div class="personnel-row">
                  <el-tooltip v-if="row.managers && row.managers.manager" content="项目经理" placement="top">
                    <el-tag size="small" type="success" class="personnel-tag">
                      <el-icon><User /></el-icon>
                      {{ getUserDisplayName(row.managers.manager) }}
                    </el-tag>
                  </el-tooltip>
                  <el-tooltip v-if="row.managers && row.managers.developer" content="开发负责人" placement="top">
                    <el-tag size="small" type="primary" class="personnel-tag">
                      <el-icon><Cpu /></el-icon>
                      {{ getUserDisplayName(row.managers.developer) }}
                    </el-tag>
                  </el-tooltip>
                  <el-tooltip v-if="row.managers && row.managers.tester" content="测试负责人" placement="top">
                    <el-tag size="small" type="warning" class="personnel-tag">
                      <el-icon><Monitor /></el-icon>
                      {{ getUserDisplayName(row.managers.tester) }}
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="!row.managers || (!row.managers.manager && !row.managers.developer && !row.managers.tester)" class="empty-personnel">
                  <el-text type="info" size="small">未配置</el-text>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="handleEdit(row)" :icon="Edit">
                  编辑
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleKubernetesResourceConfig(row)"
                  :icon="Setting"
                >
                  K8s资源配置
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

          <!-- 移动端卡片 -->
      <div class="mobile-cards mobile-only">
        <div v-if="projects.length === 0" class="empty-state">
          <el-empty description="暂无项目数据">
            <el-button type="primary" @click="handleAdd" :icon="Plus">
              新建项目
            </el-button>
          </el-empty>
        </div>
        
        <div v-else>
          <el-card 
            v-for="project in projects" 
            :key="`mobile-${project.id}`" 
            class="project-card mobile-card"
            shadow="hover"
          >
            <template #header>
              <div class="mobile-card-header">
                <div class="mobile-title">
                  <el-tag type="primary" size="small" class="code-tag">
                    {{ project.project_code }}
                  </el-tag>
                  <span class="project-name">{{ project.name }}</span>
                </div>
                <el-dropdown trigger="click">
                  <el-button type="text" class="more-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleEdit(project)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleKubernetesResourceConfig(project)">
                        <el-icon><Setting /></el-icon>
                        K8s资源配置
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleDelete(project)" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <div class="card-content">
              <div class="info-item">
                <span class="label">所属产品:</span>
                <div v-if="project.product_id" class="value">
                  <el-tag type="info" size="small">{{ getProductName(project.product_id) }}</el-tag>
                </div>
                <span v-else class="value empty-text">-</span>
              </div>

              <div class="info-item">
                <span class="label">人员配置:</span>
                <div class="personnel-config">
                  <div class="personnel-row">
                    <el-tooltip v-if="project.managers && project.managers.manager" content="项目经理" placement="top">
                      <el-tag size="small" type="success" class="personnel-tag">
                        <el-icon><User /></el-icon>
                        {{ getUserDisplayName(project.managers.manager) }}
                      </el-tag>
                    </el-tooltip>
                    <el-tooltip v-if="project.managers && project.managers.developer" content="开发负责人" placement="top">
                      <el-tag size="small" type="primary" class="personnel-tag">
                        <el-icon><Cpu /></el-icon>
                        {{ getUserDisplayName(project.managers.developer) }}
                      </el-tag>
                    </el-tooltip>
                    <el-tooltip v-if="project.managers && project.managers.tester" content="测试负责人" placement="top">
                      <el-tag size="small" type="warning" class="personnel-tag">
                        <el-icon><Monitor /></el-icon>
                        {{ getUserDisplayName(project.managers.tester) }}
                      </el-tag>
                    </el-tooltip>
                  </div>
                  <div v-if="!project.managers || (!project.managers.manager && !project.managers.developer && !project.managers.tester)" class="empty-personnel">
                    <el-text type="info" size="small">未配置</el-text>
                  </div>
                </div>
              </div>

              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(project.created_at) }}</span>
              </div>
            </div>

            <template #footer>
              <div class="mobile-card-footer">
                <el-button type="primary" size="small" @click="handleEdit(project)" :icon="Edit">
                  编辑
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleKubernetesResourceConfig(project)"
                  :icon="Setting"
                >
                  K8s配置
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(project)"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-card>
        </div>
      </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 项目表单对话框 -->
    <ProjectFormDialog
      v-model="projectDialogVisible"
      :project="currentProject || null"
      @success="handleProjectSuccess"
    />

    <!-- Kubernetes资源配置抽屉 -->
    <KubernetesResourceDrawer
      v-model="kubernetesResourceVisible"
      :project="currentProject"
      @success="handleProjectSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Edit, 
  Delete, 
  Setting, 
  List, 
  User, 
  Cpu, 
  Monitor,
  MoreFilled
} from '@element-plus/icons-vue'

import ProjectFormDialog from './components/ProjectFormDialog.vue'
import KubernetesResourceDrawer from '../components/KubernetesResourceDrawer.vue'
import { handleAPIResponse } from '../../../utils/response'
import {productApi, type Project, type Product, projectApi} from '../../../api/modules/cmdb'

// 项目相关数据
const projects = ref<Project[]>([])
const currentProject = ref<Project | null>(null)
const projectDialogVisible = ref(false)
const kubernetesResourceVisible = ref(false)

// 产品相关数据
const products = ref<Product[]>([])

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 查询条件
const searchForm = reactive({
  name: '',
  productId: null as number | null
})

// 加载状态
const loading = ref(false)

// 格式化日期
const formatDate = (date?: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

// 获取用户显示名称
const getUserDisplayName = (user: any) => {
  if (!user) return '-'
  return user.display_name || user.username || `用户${user.id}`
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      name: searchForm.name || undefined,
      product_id: searchForm.productId || undefined
    }

    const response = await projectApi.getProjects(params)
    const result = handleAPIResponse(response) as {items: Project[], total: number}
    
    if (result) {
      projects.value = result.items || []
      pagination.total = result.total || 0
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 获取产品列表
const fetchProducts = async () => {
  try {
    const response = await productApi.getProducts({ page: 1, page_size: 1000 })
    const result = handleAPIResponse(response) as {items: [], total: number}
    
    if (result) {
      products.value = result.items || []
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
  }
}

// 搜索项目
const handleSearch = () => {
  pagination.page = 1
  fetchProjects()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  searchForm.productId = null
  pagination.page = 1
  fetchProjects()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchProjects()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchProjects()
}

// 新增项目
const handleAdd = () => {
  currentProject.value = null
  projectDialogVisible.value = true
}

// 编辑项目
const handleEdit = (project: Project) => {
  currentProject.value = { ...project }
  projectDialogVisible.value = true
}

// 删除项目
const handleDelete = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${project.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await projectApi.deleteProject(project.id)
    const result = handleAPIResponse(response) as any
    
    if (result.success) {
      ElMessage.success('删除成功')
      fetchProjects()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除项目失败:', error)
      ElMessage.error('删除项目失败')
    }
  }
}

// Kubernetes资源配置
const handleKubernetesResourceConfig = (project: Project) => {
  currentProject.value = project
  kubernetesResourceVisible.value = true
}

// 项目表单提交成功
const handleProjectSuccess = () => {
  projectDialogVisible.value = false
  fetchProjects()
}

// 获取产品名称
const getProductName = (productId?: number) => {
  if (!productId) return '-'
  const product = products.value.find(p => p.id === productId)
  return product?.name || `产品${productId}`
}

// 初始化
onMounted(() => {
  fetchProducts()
  fetchProjects()
})
</script>

<style scoped>
.project-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-container {
  padding: 10px 0;
}

.search-input,
.search-select {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.icon-btn {
  padding: 8px;
}

.project-table {
  margin-bottom: 20px;
}

.code-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.project-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-weight: 500;
}

.personnel-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.personnel-row {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.personnel-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-personnel {
  color: #999;
  font-size: 12px;
}

.time-info {
  font-size: 13px;
}

.empty-text {
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .project-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
  }

  /* 移动端隐藏表格，显示卡片 */
  .desktop-table {
    display: none;
  }
  
  .mobile-cards {
    display: block;
  }
}

/* 桌面端样式 */
@media (min-width: 769px) {
  .desktop-table {
    display: block;
  }
  
  .mobile-cards {
    display: none;
  }
}

.table-header-row.desktop-only {
  display: none;
}

.data-container {
  position: relative;
}

.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.project-card.desktop-card {
  margin-bottom: 10px;
}

.project-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.row-cell {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-cell {
  font-weight: 600;
}

.mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-card-content {
  padding: 10px;
}

.info-item {
  margin-bottom: 10px;
}

.label {
  font-weight: 600;
}

.value {
  margin-left: 10px;
}

.mobile-card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}

.more-btn {
  padding: 0;
}

/* PC端样式 */
@media (min-width: 768px) {
  .desktop-only {
    display: block !important;
  }
  
  .mobile-only {
    display: none !important;
  }
  
  .table-header-row.desktop-only {
    display: flex;
    padding: 12px 16px;
    background: #f5f7fa;
    border: 1px solid #ebeef5;
    border-bottom: none;
    font-weight: 600;
    color: #606266;
  }
  
  .project-row {
    display: flex;
    align-items: center;
    gap: 0;
  }
  
  .row-cell {
    flex: none;
    padding: 0 8px;
  }
  
  .code-col {
    width: 140px;
  }
  
  .name-col {
    width: 180px;
    min-width: 180px;
  }
  
  .product-col {
    width: 150px;
  }
  
  .personnel-col {
    width: 220px;
  }
  
  .time-col {
    width: 160px;
  }
  
  .action-col {
    width: 240px;
    justify-content: center;
  }
}

/* 移动端样式 */
@media (max-width: 767px) {
  .desktop-only {
    display: none !important;
  }
  
  .mobile-only {
    display: block !important;
  }
  
  .mobile-card {
    margin-bottom: 16px;
  }
  
  .mobile-card-header {
    padding: 0;
  }
  
  .mobile-title .project-name {
    font-weight: 500;
    margin-left: 8px;
  }
  
  .mobile-card-content {
    padding: 0;
  }
  
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
  }
  
  .info-item .label {
    min-width: 80px;
    color: #606266;
    font-size: 14px;
  }
  
  .info-item .value {
    margin-left: 12px;
    flex: 1;
  }
  
  .mobile-card-footer {
    padding: 0;
    gap: 8px;
  }
  
  .mobile-card-footer .el-button {
    flex: 1;
  }
}
</style> 