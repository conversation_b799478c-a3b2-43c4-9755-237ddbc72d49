package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// JSONField 自定义JSON类型，可以存储任意JSON值
type JSONField struct {
	Data interface{}
}

// UnmarshalJSON 支持直接接收数组、对象或字符串
func (j *JSONField) UnmarshalJSON(b []byte) error {
	// 允许直接传数组、对象或字符串
	var v interface{}
	if err := json.Unmarshal(b, &v); err != nil {
		return err
	}
	j.Data = v
	return nil
}

// Value 实现driver.Valuer接口
func (j JSONField) Value() (driver.Value, error) {
	if j.Data == nil {
		return nil, nil
	}
	return json.Marshal(j.Data)
}

// Scan 实现sql.Scanner接口
func (j *JSONField) Scan(value interface{}) error {
	if value == nil {
		j.Data = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan non-string value into JSONField")
	}

	return json.Unmarshal(bytes, &j.Data)
}

// TimeAbstract 时间抽象结构 - 兼容Django数据库，不使用软删除
type TimeAbstract struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	CreatedTime time.Time `json:"created_time" gorm:"autoCreateTime;column:created_time"`
	UpdatedTime time.Time `json:"updated_time" gorm:"autoUpdateTime;column:update_time"`
}

// DBInstance 数据库实例模型
type DBInstance struct {
	TimeAbstract
	InstanceName string    `json:"instance_name" gorm:"size:50;uniqueIndex;not null;comment:实例名称"`
	Alias        string    `json:"alias" gorm:"size:200;default:'';comment:实例别名"`
	Type         string    `json:"type" gorm:"size:6;comment:实例类型" validate:"oneof=master slave"`
	DBType       string    `json:"db_type" gorm:"size:20;comment:数据库类型" validate:"oneof=mysql postgres sqlite mongodb redis oracle mssql clickhouse"`
	Mode         string    `json:"mode" gorm:"size:10;default:'';comment:运行模式" validate:"oneof=standalone cluster"`
	Host         string    `json:"host" gorm:"size:200;not null;comment:实例连接"`
	Port         int       `json:"port" gorm:"default:0;comment:端口"`
	User         string    `json:"user" gorm:"size:200;default:'';comment:用户名"`
	Password     string    `json:"password" gorm:"size:300;default:'';comment:密码"`
	DBName       string    `json:"db_name" gorm:"size:64;default:'';comment:数据库名"`
	Charset      string    `json:"charset" gorm:"size:20;default:'utf8mb4';comment:字符集"`
	AutoBackup   int       `json:"auto_backup" gorm:"default:0;comment:是否自动备份"`
	Projects     JSONField `json:"projects" gorm:"type:jsonb;comment:所属项目"`
	Environments JSONField `json:"environments" gorm:"type:jsonb;comment:所属环境"`
	IsDeleted    int       `json:"is_deleted" gorm:"default:0;comment:是否删除"`
}

// TableName 指定表名
func (DBInstance) TableName() string {
	return "sql_dbinstance"
}

// SqlWorkflow SQL工单模型
type SqlWorkflow struct {
	TimeAbstract
	OrderID         string      `json:"order_id" gorm:"size:40;uniqueIndex;not null;comment:工单号"`
	Title           string      `json:"title" gorm:"size:100;not null;comment:工单标题"`
	InstanceID      uint        `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance        *DBInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	DBName          string      `json:"db_name" gorm:"size:64;not null;comment:数据库"`
	SchemaName      string      `json:"schema_name" gorm:"size:64;comment:模式"`
	SyntaxType      int         `json:"syntax_type" gorm:"default:0;comment:工单类型 0、未知，1、DDL，2、DML"`
	IsBackup        bool        `json:"is_backup" gorm:"default:true;comment:是否备份"`
	Engineer        string      `json:"engineer" gorm:"size:30;default:'';comment:发起人"`
	EngineerDisplay string      `json:"engineer_display" gorm:"size:50;default:'';comment:发起人中文名"`
	Status          string      `json:"status" gorm:"size:50;not null;comment:工单状态"`
	RunDateStart    *time.Time  `json:"run_date_start" gorm:"comment:可执行起始时间"`
	RunDateEnd      *time.Time  `json:"run_date_end" gorm:"comment:可执行结束时间"`
	IsManual        int         `json:"is_manual" gorm:"default:0;comment:是否原生执行"`
	SqlContent      string      `json:"sql_content" gorm:"type:text;not null;comment:具体sql内容"`
	Method          string      `json:"method" gorm:"size:6;default:'manual';comment:上线方式"`
	ExpectTime      *time.Time  `json:"expect_time" gorm:"comment:期望上线时间"`
	RelatedOID      string      `json:"related_oid" gorm:"size:80;comment:关联工单ID"`
}

// TableName 指定表名
func (SqlWorkflow) TableName() string {
	return "sql_sqlworkflow"
}

// SqlWorkflowResult SQL工单结果模型
type SqlWorkflowResult struct {
	TimeAbstract
	SqlWorkflowID int       `json:"sqlworkflow_id" gorm:"default:0;comment:SQL上线工单ID"`
	ReviewContent JSONField `json:"review_content" gorm:"type:jsonb;comment:自动审核内容的JSON格式"`
	ExecuteResult JSONField `json:"execute_result" gorm:"type:jsonb;comment:执行结果的JSON格式"`
	DeployerID    *uint     `json:"deployer_id" gorm:"comment:部署人ID"`
	CheckMethod   string    `json:"check_method" gorm:"size:6;default:'manual';comment:审核方式"`
}

// TableName 指定表名
func (SqlWorkflowResult) TableName() string {
	return "sql_sqlworkflowresult"
}

// SqlQueryLog SQL查询日志模型
type SqlQueryLog struct {
	TimeAbstract
	InstanceID uint        `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance   *DBInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	DBName     string      `json:"db_name" gorm:"size:64;not null;comment:数据库名称"`
	SchemaName string      `json:"schema_name" gorm:"size:64;comment:模式"`
	SqlLog     string      `json:"sqllog" gorm:"type:text;not null;comment:执行的查询语句"`
	EffectRow  int64       `json:"effect_row" gorm:"default:0;comment:返回行数"`
	CostTime   string      `json:"cost_time" gorm:"size:32;default:'';comment:执行耗时"`
	DeployerID *uint       `json:"deployer_id" gorm:"comment:执行人ID"`
	HitRule    bool        `json:"hit_rule" gorm:"default:false;comment:查询是否命中脱敏规则"`
	Masking    bool        `json:"masking" gorm:"default:false;comment:查询结果是否正常脱敏"`
	Favorite   bool        `json:"favorite" gorm:"default:false;comment:是否收藏"`
	Alias      string      `json:"alias" gorm:"size:64;default:'';comment:语句标识"`
}

// TableName 指定表名
func (SqlQueryLog) TableName() string {
	return "sql_sqlquerylog"
}

// SqlRollback SQL回滚记录模型
type SqlRollback struct {
	TimeAbstract
	WorkflowID   uint         `json:"workflow_id" gorm:"not null;comment:工单ID"`
	Workflow     *SqlWorkflow `json:"workflow,omitempty" gorm:"foreignKey:WorkflowID;constraint:OnDelete:CASCADE"`
	InstanceID   uint         `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance     *DBInstance  `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	DBName       string       `json:"db_name" gorm:"size:64;not null;comment:数据库名"`
	BackupDBName string       `json:"backup_db_name" gorm:"size:64;comment:备份数据库名"`
	SqlContent   string       `json:"sql_content" gorm:"type:text;not null;comment:原始SQL"`
	RollbackSQL  string       `json:"rollback_sql" gorm:"type:text;comment:回滚SQL"`
	Status       string       `json:"status" gorm:"size:32;default:pending;comment:回滚状态"` // pending, success, failed
	ExecutedBy   string       `json:"executed_by" gorm:"size:64;comment:执行人"`
	ExecutedAt   *time.Time   `json:"executed_at" gorm:"comment:执行时间"`
	ErrorMsg     string       `json:"error_msg" gorm:"type:text;comment:错误信息"`
}

// TableName 指定表名
func (SqlRollback) TableName() string {
	return "sql_sqlrollback"
}

// SqlQueryAdvisor SQL优化日志模型
type SqlQueryAdvisor struct {
	TimeAbstract
	InstanceID uint        `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance   *DBInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	DBName     string      `json:"db_name" gorm:"size:64;not null;comment:数据库名称"`
	SchemaName string      `json:"schema_name" gorm:"size:64;comment:模式"`
	SqlLog     string      `json:"sqllog" gorm:"type:text;not null;comment:执行的查询语句"`
	DeployerID *uint       `json:"deployer_id" gorm:"comment:执行人ID"`
	Tool       string      `json:"tool" gorm:"size:16;default:'soar';comment:优化工具"`
	Result     JSONField   `json:"result" gorm:"type:jsonb;comment:优化结果"`
	Favorite   bool        `json:"favorite" gorm:"default:false;comment:是否收藏"`
}

// TableName 指定表名
func (SqlQueryAdvisor) TableName() string {
	return "sql_sqlqueryadvisor"
}

// DataMaskingColumns 数据脱敏配置模型
type DataMaskingColumns struct {
	TimeAbstract
	Active      bool        `json:"active" gorm:"not null;comment:激活状态"`
	InstanceID  uint        `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance    *DBInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	TableSchema string      `json:"table_schema" gorm:"size:64;not null;comment:字段所在库名"`
	TbName      string      `json:"table_name" gorm:"size:64;not null;comment:字段所在表名"`
	Columns     JSONField   `json:"columns" gorm:"type:jsonb;comment:字段数组"`
}

// TableName 指定表名
func (DataMaskingColumns) TableName() string {
	return "sql_datamaskingcolumns"
}

// QueryPrivileges 查询权限模型
type QueryPrivileges struct {
	TimeAbstract
	Username    string      `json:"username" gorm:"size:50;not null;comment:用户名"`
	UserDisplay string      `json:"user_display" gorm:"size:50;default:'';comment:姓名"`
	InstanceID  uint        `json:"instance_id" gorm:"not null;comment:数据库实例ID"`
	Instance    *DBInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID;constraint:OnDelete:CASCADE"`
	DBName      string      `json:"db_name" gorm:"size:64;default:'';comment:数据库"`
	SchemaName  string      `json:"schema_name" gorm:"size:64;comment:模式"`
	ValidDate   time.Time   `json:"valid_date" gorm:"not null;comment:有效时间"`
	LimitNum    int         `json:"limit_num" gorm:"default:100;comment:行数限制"`
	IsDeleted   int         `json:"is_deleted" gorm:"default:0;comment:是否删除"`
}

// TableName 指定表名
func (QueryPrivileges) TableName() string {
	return "sql_queryprivileges"
}

// 请求和响应数据传输对象

// DBInstanceCreateRequest 创建数据库实例请求
type DBInstanceCreateRequest struct {
	InstanceName string    `json:"instance_name" binding:"required" validate:"max=50"`
	Alias        string    `json:"alias" validate:"max=200"`
	Type         string    `json:"type" binding:"required" validate:"oneof=master slave"`
	DBType       string    `json:"db_type" binding:"required" validate:"oneof=mysql postgres sqlite mongodb redis oracle mssql clickhouse"`
	Mode         string    `json:"mode" validate:"oneof=standalone cluster"`
	Host         string    `json:"host" binding:"required" validate:"max=200"`
	Port         int       `json:"port" binding:"required" validate:"min=1,max=65535"`
	User         string    `json:"user" validate:"max=200"`
	Password     string    `json:"password" validate:"max=300"`
	DBName       string    `json:"db_name" validate:"max=64"`
	Charset      string    `json:"charset" validate:"max=20"`
	AutoBackup   int       `json:"auto_backup" validate:"oneof=0 1"`
	Projects     JSONField `json:"projects"`
	Environments JSONField `json:"environments"`
}

// SqlWorkflowCreateRequest 创建SQL工单请求
type SqlWorkflowCreateRequest struct {
	Title      string     `json:"title" binding:"required" validate:"max=100"`
	InstanceID uint       `json:"instance_id" binding:"required"`
	DBName     string     `json:"db_name" binding:"required" validate:"max=64"`
	SchemaName string     `json:"schema_name" validate:"max=64"`
	IsBackup   bool       `json:"is_backup"`
	SqlContent string     `json:"sql_content" binding:"required"`
	Method     string     `json:"method" validate:"oneof=manual auto plan"`
	ExpectTime *time.Time `json:"expect_time"`
	RelatedOID string     `json:"related_oid" validate:"max=80"`
}

// SqlQueryRequest SQL查询请求
type SqlQueryRequest struct {
	SqlContent  string   `json:"sql_content" binding:"required"`
	DBName      string   `json:"db_name" binding:"required"`
	SchemaName  string   `json:"schema_name"`
	LimitNum    int      `json:"limit_num" validate:"min=1,max=10000"`
	Offset      int      `json:"offset" validate:"min=0"`
	Click       bool     `json:"click"`
	TbName      string   `json:"tb_name"`
	OrderColumn string   `json:"order_column"`
	Order       string   `json:"order" validate:"oneof=ASC DESC"`
	Columns     []string `json:"columns"`
}

// DataMaskingColumnsCreateRequest 创建脱敏配置请求
type DataMaskingColumnsCreateRequest struct {
	Active      bool      `json:"active" binding:"required"`
	InstanceID  uint      `json:"instance_id" binding:"required"`
	TableSchema string    `json:"table_schema" binding:"required" validate:"max=64"`
	TableName   string    `json:"table_name" binding:"required" validate:"max=64"`
	Columns     JSONField `json:"columns" binding:"required"`
}

// QueryPrivilegesCreateRequest 创建查询权限请求
type QueryPrivilegesCreateRequest struct {
	Username    string    `json:"username" binding:"required" validate:"max=50"`
	UserDisplay string    `json:"user_display" validate:"max=50"`
	InstanceID  uint      `json:"instance_id" binding:"required"`
	DBName      string    `json:"db_name" validate:"max=64"`
	SchemaName  string    `json:"schema_name" validate:"max=64"`
	ValidDate   time.Time `json:"valid_date" binding:"required"`
	LimitNum    int       `json:"limit_num" validate:"min=1,max=10000"`
}

// SqlRollbackRequest SQL回滚请求
type SqlRollbackRequest struct {
	WorkflowID uint   `json:"workflow_id" binding:"required"`
	Reason     string `json:"reason" validate:"max=500"`
}

// SqlRollbackResponse SQL回滚响应
type SqlRollbackResponse struct {
	ID          uint       `json:"id"`
	WorkflowID  uint       `json:"workflow_id"`
	Status      string     `json:"status"`
	RollbackSQL string     `json:"rollback_sql,omitempty"`
	ExecutedBy  string     `json:"executed_by,omitempty"`
	ExecutedAt  *time.Time `json:"executed_at,omitempty"`
	ErrorMsg    string     `json:"error_msg,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
}

// 通用响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 分页响应结构
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// SQL查询结果结构
type QueryResult struct {
	Columns             []string        `json:"column_list"`
	Rows                [][]interface{} `json:"rows"`
	AffectedRows        int64           `json:"affected_rows"`
	QueryTime           string          `json:"query_time"`
	SecondsBehindMaster int             `json:"seconds_behind_master"`
	Error               string          `json:"error,omitempty"`
	ID                  uint            `json:"id,omitempty"`
}

// SQL检查结果结构
type CheckResult struct {
	ID           int    `json:"id"`
	Stage        string `json:"stage"`
	ErrLevel     int    `json:"errlevel"`
	StageStatus  string `json:"stagestatus"`
	ErrorMessage string `json:"errormessage"`
	SQL          string `json:"sql"`
	AffectedRows int64  `json:"affected_rows"`
	Sequence     string `json:"sequence"`
	BackupDBName string `json:"backup_dbname"`
	ExecuteTime  string `json:"execute_time"`
	SqlSha1      string `json:"sqlsha1"`
	BackupTime   string `json:"backup_time"`
}

// 数据库迁移函数
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
	// &DBInstance{},
	// &SqlWorkflow{},
	// &SqlWorkflowResult{},
	// &SqlQueryLog{},
	// &SqlRollback{},
	// &SqlQueryAdvisor{},
	// &DataMaskingColumns{},
	// &QueryPrivileges{},
	)
}
